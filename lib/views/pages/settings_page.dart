import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import '../../controllers/settings_controller.dart';
import '../../providers/settings_provider.dart';
import '../../providers/user_provider.dart';
import '../../models/app_settings_model.dart';
import '../../core/constants/app_colors.dart';
import '../../core/routes/app_routes.dart';

class SettingsPage extends GetView<SettingsController> {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: Consumer2<SettingsProvider, UserProvider>(
        builder: (context, settingsProvider, userProvider, child) {
          return ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Theme Section
              _buildSectionHeader('Appearance'),
              _buildThemeSelector(settingsProvider),

              const SizedBox(height: 24),

              // Notifications Section
              _buildSectionHeader('Notifications'),
              _buildSwitchTile(
                title: 'Push Notifications',
                subtitle: 'Receive notifications about games and achievements',
                value: settingsProvider.settings.notificationsEnabled,
                onChanged: (value) => settingsProvider.toggleNotifications(),
              ),

              const SizedBox(height: 24),

              // Audio Section
              _buildSectionHeader('Audio'),
              _buildSwitchTile(
                title: 'Sound Effects',
                subtitle: 'Play sound effects in games',
                value: settingsProvider.settings.soundEnabled,
                onChanged: (value) => settingsProvider.toggleSound(),
              ),
              _buildSwitchTile(
                title: 'Vibration',
                subtitle: 'Vibrate on game events',
                value: settingsProvider.settings.vibrationEnabled,
                onChanged: (value) => settingsProvider.toggleVibration(),
              ),

              const SizedBox(height: 24),

              // Account Section
              _buildSectionHeader('Account'),
              _buildListTile(
                icon: Icons.person,
                title: 'Profile',
                subtitle: 'Manage your profile information',
                onTap: () => Get.toNamed(AppRoutes.profile),
              ),
              _buildListTile(
                icon: Icons.logout,
                title: 'Sign Out',
                subtitle: 'Sign out of your account',
                onTap: () => _showLogoutDialog(context, userProvider),
              ),

              const SizedBox(height: 24),

              // Support Section
              _buildSectionHeader('Support'),
              _buildListTile(
                icon: Icons.help,
                title: 'Help & Support',
                subtitle: 'Get help and contact support',
                onTap: () => Get.toNamed(AppRoutes.help),
              ),
              _buildListTile(
                icon: Icons.info,
                title: 'About',
                subtitle: 'App version and information',
                onTap: () => Get.toNamed(AppRoutes.about),
              ),
              _buildListTile(
                icon: Icons.privacy_tip,
                title: 'Privacy Policy',
                subtitle: 'Read our privacy policy',
                onTap: () => Get.toNamed(AppRoutes.privacyPolicy),
              ),
              _buildListTile(
                icon: Icons.description,
                title: 'Terms of Service',
                subtitle: 'Read our terms of service',
                onTap: () => Get.toNamed(AppRoutes.termsOfService),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppColors.primary,
        ),
      ),
    );
  }

  Widget _buildThemeSelector(SettingsProvider settingsProvider) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.lightGrey),
      ),
      child: Column(
        children: [
          _buildThemeOption(
            title: 'Light Theme',
            value: AppThemeMode.light,
            groupValue: settingsProvider.themeMode,
            onChanged: (value) => settingsProvider.changeThemeMode(value!),
          ),
          _buildThemeOption(
            title: 'Dark Theme',
            value: AppThemeMode.dark,
            groupValue: settingsProvider.themeMode,
            onChanged: (value) => settingsProvider.changeThemeMode(value!),
          ),
          _buildThemeOption(
            title: 'System Default',
            value: AppThemeMode.system,
            groupValue: settingsProvider.themeMode,
            onChanged: (value) => settingsProvider.changeThemeMode(value!),
          ),
        ],
      ),
    );
  }

  Widget _buildThemeOption({
    required String title,
    required AppThemeMode value,
    required AppThemeMode groupValue,
    required ValueChanged<AppThemeMode?> onChanged,
  }) {
    return RadioListTile<AppThemeMode>(
      title: Text(title),
      value: value,
      groupValue: groupValue,
      onChanged: onChanged,
      activeColor: AppColors.primary,
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.lightGrey),
      ),
      child: SwitchListTile(
        title: Text(title),
        subtitle: Text(subtitle),
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.primary,
      ),
    );
  }

  Widget _buildListTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: AppColors.surfaceLight,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.lightGrey),
      ),
      child: ListTile(
        leading: Icon(icon, color: AppColors.primary),
        title: Text(title),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.chevron_right),
        onTap: onTap,
      ),
    );
  }

  void _showLogoutDialog(BuildContext context, UserProvider userProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sign Out'),
        content: const Text('Are you sure you want to sign out?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await userProvider.logoutUser();
              Get.offAllNamed(AppRoutes.login);
            },
            child: const Text('Sign Out'),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../core/routes/app_routes.dart';

class OnboardingController extends GetxController {
  final PageController pageController = PageController();
  final RxInt currentPage = 0.obs;
  final RxBool isLastPage = false.obs;

  final List<Map<String, dynamic>> onboardingItems = [
    {
      'icon': Icons.games,
      'title': 'Discover Amazing Games',
      'description': 'Explore a vast collection of games tailored to your preferences and skill level.',
    },
    {
      'icon': Icons.leaderboard,
      'title': 'Compete & Climb',
      'description': 'Challenge other players and climb the leaderboards to prove your gaming prowess.',
    },
    {
      'icon': Icons.emoji_events,
      'title': 'Unlock Achievements',
      'description': 'Complete challenges and unlock achievements to showcase your gaming milestones.',
    },
  ];

  @override
  void onClose() {
    pageController.dispose();
    super.onClose();
  }

  void onPageChanged(int page) {
    currentPage.value = page;
    isLastPage.value = page == onboardingItems.length - 1;
  }

  void nextPage() {
    if (currentPage.value < onboardingItems.length - 1) {
      pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void skip() {
    _completeOnboarding();
  }

  void finish() {
    _completeOnboarding();
  }

  Future<void> _completeOnboarding() async {
    try {
      // Mark onboarding as completed
      final context = Get.context!;
      final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
      await settingsProvider.completeOnboarding();

      // Navigate to login
      Get.offAllNamed(AppRoutes.login);
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to complete onboarding: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}

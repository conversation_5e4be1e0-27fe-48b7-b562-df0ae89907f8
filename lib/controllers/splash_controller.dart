import 'package:get/get.dart';
import 'package:provider/provider.dart';
import '../providers/user_provider.dart';
import '../providers/settings_provider.dart';
import '../core/routes/app_routes.dart';

class SplashController extends GetxController {
  final RxBool isLoading = true.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      // Simulate loading time
      await Future.delayed(const Duration(seconds: 2));

      // Get providers from context
      final context = Get.context!;
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final settingsProvider = Provider.of<SettingsProvider>(context, listen: false);

      // Initialize providers
      await Future.wait([
        userProvider.initializeUser(),
        settingsProvider.initializeSettings(),
      ]);

      // Navigate based on app state
      _navigateToNextScreen(userProvider, settingsProvider);
    } catch (e) {
      // Handle initialization error
      Get.snackbar(
        'Error',
        'Failed to initialize app: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
      
      // Navigate to error page or retry
      Get.offAllNamed(AppRoutes.error);
    } finally {
      isLoading.value = false;
    }
  }

  void _navigateToNextScreen(UserProvider userProvider, SettingsProvider settingsProvider) {
    // Check if onboarding is completed
    if (!settingsProvider.settings.onboardingCompleted) {
      Get.offAllNamed(AppRoutes.onboarding);
      return;
    }

    // Check if user is logged in
    if (userProvider.isLoggedIn) {
      Get.offAllNamed(AppRoutes.home);
    } else {
      Get.offAllNamed(AppRoutes.login);
    }
  }
}

import 'package:get/get.dart';
import '../core/routes/app_routes.dart';

class HomeController extends GetxController {
  final RxInt currentIndex = 0.obs;

  @override
  void onInit() {
    super.onInit();
    _loadHomeData();
  }

  void onTabChanged(int index) {
    currentIndex.value = index;
    
    // Navigate to different pages based on tab selection
    switch (index) {
      case 0:
        // Already on home, do nothing
        break;
      case 1:
        Get.toNamed(AppRoutes.games);
        break;
      case 2:
        Get.toNamed(AppRoutes.leaderboard);
        break;
      case 3:
        Get.toNamed(AppRoutes.profile);
        break;
    }
  }

  Future<void> _loadHomeData() async {
    try {
      // Load home page data here
      // This could include recent games, achievements, etc.
      await Future.delayed(const Duration(milliseconds: 500));
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to load home data: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  void refreshData() {
    _loadHomeData();
  }
}

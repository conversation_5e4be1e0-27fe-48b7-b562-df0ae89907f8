import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';

import 'core/themes/app_theme.dart';
import 'core/routes/app_pages.dart';
import 'core/constants/app_constants.dart';
import 'providers/user_provider.dart';
import 'providers/settings_provider.dart';
import 'services/storage_service.dart';
import 'services/notification_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialize services
  await _initializeServices();
  
  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );
  
  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);
  
  runApp(const GamefyApp());
}

Future<void> _initializeServices() async {
  // Initialize storage service
  await Get.putAsync(() => StorageService().init());
  
  // Initialize notification service
  await Get.putAsync(() => NotificationService().init());
}

class GamefyApp extends StatelessWidget {
  const GamefyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => UserProvider()),
        ChangeNotifierProvider(create: (_) => SettingsProvider()),
      ],
      child: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          return GetMaterialApp(
            // App Configuration
            title: AppConstants.appName,
            debugShowCheckedModeBanner: false,
            
            // Theme Configuration
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: settingsProvider.flutterThemeMode,
            
            // Localization
            locale: settingsProvider.settings.locale,
            fallbackLocale: const Locale('en', 'US'),
            
            // Navigation Configuration
            initialRoute: AppPages.initial,
            getPages: AppPages.pages,
            unknownRoute: GetPage(
              name: '/404',
              page: () => const Scaffold(
                body: Center(
                  child: Text('Page not found'),
                ),
              ),
            ),
            
            // Route Guards
            routingCallback: (routing) {
              // Add route guards here if needed
              debugPrint('Navigating to: ${routing?.current}');
            },
            
            // Global Bindings
            initialBinding: InitialBinding(),
            
            // Error Handling
            builder: (context, child) {
              return MediaQuery(
                data: MediaQuery.of(context).copyWith(
                  textScaleFactor: settingsProvider.settings.fontSize / 14.0,
                ),
                child: child ?? const SizedBox.shrink(),
              );
            },
          );
        },
      ),
    );
  }
}

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Initialize providers
    final userProvider = Get.find<UserProvider>();
    final settingsProvider = Get.find<SettingsProvider>();
    
    // Initialize app state
    userProvider.initializeUser();
    settingsProvider.initializeSettings();
  }
}

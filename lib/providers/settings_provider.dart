import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import '../models/app_settings_model.dart';
import '../core/constants/app_constants.dart';

class SettingsProvider extends ChangeNotifier {
  AppSettingsModel _settings = const AppSettingsModel();
  bool _isLoading = false;
  String? _error;

  // Getters
  AppSettingsModel get settings => _settings;
  bool get isLoading => _isLoading;
  String? get error => _error;
  
  // Theme-related getters
  AppThemeMode get themeMode => _settings.themeMode;
  ThemeMode get flutterThemeMode => _settings.flutterThemeMode;
  bool get isDarkMode => _settings.themeMode == AppThemeMode.dark;
  bool get isLightMode => _settings.themeMode == AppThemeMode.light;
  bool get isSystemMode => _settings.themeMode == AppThemeMode.system;

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Set error state
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Initialize settings from storage
  Future<void> initializeSettings() async {
    _setLoading(true);
    _setError(null);

    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(AppConstants.settingsKey);

      if (settingsJson != null) {
        final settingsMap = json.decode(settingsJson) as Map<String, dynamic>;
        _settings = AppSettingsModel.fromJson(settingsMap);
      }
      
      debugPrint('Settings initialized: ${_settings.toString()}');
    } catch (e) {
      _setError('Failed to load settings: ${e.toString()}');
      debugPrint('Error initializing settings: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Update settings
  Future<void> updateSettings(AppSettingsModel newSettings) async {
    _setLoading(true);
    _setError(null);

    try {
      await _saveSettingsToStorage(newSettings);
      _settings = newSettings;
      debugPrint('Settings updated: ${newSettings.toString()}');
    } catch (e) {
      _setError('Failed to update settings: ${e.toString()}');
      debugPrint('Error updating settings: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Change theme mode
  Future<void> changeThemeMode(AppThemeMode themeMode) async {
    final newSettings = _settings.copyWith(themeMode: themeMode);
    await updateSettings(newSettings);
  }

  // Toggle between light and dark theme
  Future<void> toggleTheme() async {
    final newThemeMode = _settings.themeMode == AppThemeMode.light
        ? AppThemeMode.dark
        : AppThemeMode.light;
    await changeThemeMode(newThemeMode);
  }

  // Change locale
  Future<void> changeLocale(Locale locale) async {
    final newSettings = _settings.copyWith(locale: locale);
    await updateSettings(newSettings);
  }

  // Toggle notifications
  Future<void> toggleNotifications() async {
    final newSettings = _settings.copyWith(
      notificationsEnabled: !_settings.notificationsEnabled,
    );
    await updateSettings(newSettings);
  }

  // Toggle sound
  Future<void> toggleSound() async {
    final newSettings = _settings.copyWith(
      soundEnabled: !_settings.soundEnabled,
    );
    await updateSettings(newSettings);
  }

  // Toggle vibration
  Future<void> toggleVibration() async {
    final newSettings = _settings.copyWith(
      vibrationEnabled: !_settings.vibrationEnabled,
    );
    await updateSettings(newSettings);
  }

  // Change font size
  Future<void> changeFontSize(double fontSize) async {
    final newSettings = _settings.copyWith(fontSize: fontSize);
    await updateSettings(newSettings);
  }

  // Mark onboarding as completed
  Future<void> completeOnboarding() async {
    final newSettings = _settings.copyWith(onboardingCompleted: true);
    await updateSettings(newSettings);
  }

  // Reset onboarding
  Future<void> resetOnboarding() async {
    final newSettings = _settings.copyWith(onboardingCompleted: false);
    await updateSettings(newSettings);
  }

  // Update custom setting
  Future<void> updateCustomSetting(String key, dynamic value) async {
    final currentCustomSettings = Map<String, dynamic>.from(_settings.customSettings ?? {});
    currentCustomSettings[key] = value;
    
    final newSettings = _settings.copyWith(customSettings: currentCustomSettings);
    await updateSettings(newSettings);
  }

  // Get custom setting
  T? getCustomSetting<T>(String key) {
    if (_settings.customSettings == null) return null;
    return _settings.customSettings![key] as T?;
  }

  // Reset settings to default
  Future<void> resetSettings() async {
    const defaultSettings = AppSettingsModel();
    await updateSettings(defaultSettings);
  }

  // Clear error
  void clearError() {
    _setError(null);
  }

  // Private method to save settings to storage
  Future<void> _saveSettingsToStorage(AppSettingsModel settings) async {
    final prefs = await SharedPreferences.getInstance();
    final settingsJson = json.encode(settings.toJson());
    await prefs.setString(AppConstants.settingsKey, settingsJson);
  }
}

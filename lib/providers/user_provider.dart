import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import '../models/user_model.dart';
import '../core/constants/app_constants.dart';

class UserProvider extends ChangeNotifier {
  UserModel? _user;
  bool _isLoading = false;
  String? _error;

  // Getters
  UserModel? get user => _user;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isLoggedIn => _user != null;

  // Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Set error state
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

  // Initialize user data from storage
  Future<void> initializeUser() async {
    _setLoading(true);
    _setError(null);

    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(AppConstants.userDataKey);

      if (userJson != null) {
        final userMap = json.decode(userJson) as Map<String, dynamic>;
        _user = UserModel.fromJson(userMap);
      }
    } catch (e) {
      _setError('Failed to load user data: ${e.toString()}');
      debugPrint('Error initializing user: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Login user
  Future<void> loginUser(UserModel user) async {
    _setLoading(true);
    _setError(null);

    try {
      // Update last login time
      final updatedUser = user.copyWith(lastLoginAt: DateTime.now());
      
      // Save to storage
      await _saveUserToStorage(updatedUser);
      
      _user = updatedUser;
      debugPrint('User logged in: ${user.name}');
    } catch (e) {
      _setError('Failed to login: ${e.toString()}');
      debugPrint('Error logging in user: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Update user data
  Future<void> updateUser(UserModel updatedUser) async {
    _setLoading(true);
    _setError(null);

    try {
      await _saveUserToStorage(updatedUser);
      _user = updatedUser;
      debugPrint('User updated: ${updatedUser.name}');
    } catch (e) {
      _setError('Failed to update user: ${e.toString()}');
      debugPrint('Error updating user: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Update user preferences
  Future<void> updateUserPreferences(Map<String, dynamic> preferences) async {
    if (_user == null) return;

    final updatedUser = _user!.copyWith(preferences: preferences);
    await updateUser(updatedUser);
  }

  // Logout user
  Future<void> logoutUser() async {
    _setLoading(true);
    _setError(null);

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(AppConstants.userDataKey);
      
      _user = null;
      debugPrint('User logged out');
    } catch (e) {
      _setError('Failed to logout: ${e.toString()}');
      debugPrint('Error logging out user: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Clear error
  void clearError() {
    _setError(null);
  }

  // Private method to save user to storage
  Future<void> _saveUserToStorage(UserModel user) async {
    final prefs = await SharedPreferences.getInstance();
    final userJson = json.encode(user.toJson());
    await prefs.setString(AppConstants.userDataKey, userJson);
  }

  // Get user preference by key
  T? getUserPreference<T>(String key) {
    if (_user?.preferences == null) return null;
    return _user!.preferences![key] as T?;
  }

  // Set user preference
  Future<void> setUserPreference<T>(String key, T value) async {
    if (_user == null) return;

    final currentPreferences = Map<String, dynamic>.from(_user!.preferences ?? {});
    currentPreferences[key] = value;
    
    await updateUserPreferences(currentPreferences);
  }
}

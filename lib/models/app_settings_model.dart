import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

enum AppThemeMode { light, dark, system }

class AppSettingsModel extends Equatable {
  final AppThemeMode themeMode;
  final Locale locale;
  final bool notificationsEnabled;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final double fontSize;
  final bool onboardingCompleted;
  final Map<String, dynamic>? customSettings;

  const AppSettingsModel({
    this.themeMode = AppThemeMode.system,
    this.locale = const Locale('en', 'US'),
    this.notificationsEnabled = true,
    this.soundEnabled = true,
    this.vibrationEnabled = true,
    this.fontSize = 14.0,
    this.onboardingCompleted = false,
    this.customSettings,
  });

  // Factory constructor for creating AppSettingsModel from JSON
  factory AppSettingsModel.fromJson(Map<String, dynamic> json) {
    return AppSettingsModel(
      themeMode: AppThemeMode.values.firstWhere(
        (mode) => mode.name == json['theme_mode'],
        orElse: () => AppThemeMode.system,
      ),
      locale: Locale(
        json['locale_language_code'] as String? ?? 'en',
        json['locale_country_code'] as String? ?? 'US',
      ),
      notificationsEnabled: json['notifications_enabled'] as bool? ?? true,
      soundEnabled: json['sound_enabled'] as bool? ?? true,
      vibrationEnabled: json['vibration_enabled'] as bool? ?? true,
      fontSize: (json['font_size'] as num?)?.toDouble() ?? 14.0,
      onboardingCompleted: json['onboarding_completed'] as bool? ?? false,
      customSettings: json['custom_settings'] as Map<String, dynamic>?,
    );
  }

  // Method to convert AppSettingsModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'theme_mode': themeMode.name,
      'locale_language_code': locale.languageCode,
      'locale_country_code': locale.countryCode,
      'notifications_enabled': notificationsEnabled,
      'sound_enabled': soundEnabled,
      'vibration_enabled': vibrationEnabled,
      'font_size': fontSize,
      'onboarding_completed': onboardingCompleted,
      'custom_settings': customSettings,
    };
  }

  // CopyWith method for creating modified copies
  AppSettingsModel copyWith({
    AppThemeMode? themeMode,
    Locale? locale,
    bool? notificationsEnabled,
    bool? soundEnabled,
    bool? vibrationEnabled,
    double? fontSize,
    bool? onboardingCompleted,
    Map<String, dynamic>? customSettings,
  }) {
    return AppSettingsModel(
      themeMode: themeMode ?? this.themeMode,
      locale: locale ?? this.locale,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      fontSize: fontSize ?? this.fontSize,
      onboardingCompleted: onboardingCompleted ?? this.onboardingCompleted,
      customSettings: customSettings ?? this.customSettings,
    );
  }

  // Helper method to get ThemeMode for Flutter
  ThemeMode get flutterThemeMode {
    switch (themeMode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }

  @override
  List<Object?> get props => [
        themeMode,
        locale,
        notificationsEnabled,
        soundEnabled,
        vibrationEnabled,
        fontSize,
        onboardingCompleted,
        customSettings,
      ];

  @override
  String toString() {
    return 'AppSettingsModel(themeMode: $themeMode, locale: $locale, onboardingCompleted: $onboardingCompleted)';
  }
}

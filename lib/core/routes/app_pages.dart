import 'package:get/get.dart';

import 'app_routes.dart';
import '../../views/pages/splash_page.dart';
import '../../views/pages/onboarding_page.dart';
import '../../views/pages/auth/login_page.dart';
import '../../views/pages/auth/register_page.dart';
import '../../views/pages/auth/forgot_password_page.dart';
import '../../views/pages/home_page.dart';
import '../../views/pages/dashboard_page.dart';
import '../../views/pages/profile_page.dart';
import '../../views/pages/settings_page.dart';
import '../../views/pages/games/games_page.dart';
import '../../views/pages/games/game_details_page.dart';
import '../../views/pages/leaderboard_page.dart';
import '../../views/pages/achievements_page.dart';
import '../../views/pages/notifications_page.dart';
import '../../views/pages/help_page.dart';
import '../../views/pages/about_page.dart';
import '../../views/pages/privacy_policy_page.dart';
import '../../views/pages/terms_of_service_page.dart';
import '../../views/pages/not_found_page.dart';
import '../../views/pages/error_page.dart';

import '../../controllers/splash_controller.dart';
import '../../controllers/onboarding_controller.dart';
import '../../controllers/auth/login_controller.dart';
import '../../controllers/auth/register_controller.dart';
import '../../controllers/auth/forgot_password_controller.dart';
import '../../controllers/home_controller.dart';
import '../../controllers/dashboard_controller.dart';
import '../../controllers/profile_controller.dart';
import '../../controllers/settings_controller.dart';
import '../../controllers/games/games_controller.dart';
import '../../controllers/games/game_details_controller.dart';
import '../../controllers/leaderboard_controller.dart';
import '../../controllers/achievements_controller.dart';
import '../../controllers/notifications_controller.dart';

class AppPages {
  static const String initial = AppRoutes.splash;

  static final List<GetPage> pages = [
    // Splash & Onboarding
    GetPage(
      name: AppRoutes.splash,
      page: () => const SplashPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<SplashController>(() => SplashController());
      }),
    ),
    
    GetPage(
      name: AppRoutes.onboarding,
      page: () => const OnboardingPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<OnboardingController>(() => OnboardingController());
      }),
    ),

    // Authentication
    GetPage(
      name: AppRoutes.login,
      page: () => const LoginPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<LoginController>(() => LoginController());
      }),
    ),
    
    GetPage(
      name: AppRoutes.register,
      page: () => const RegisterPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<RegisterController>(() => RegisterController());
      }),
    ),
    
    GetPage(
      name: AppRoutes.forgotPassword,
      page: () => const ForgotPasswordPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<ForgotPasswordController>(() => ForgotPasswordController());
      }),
    ),

    // Main App
    GetPage(
      name: AppRoutes.home,
      page: () => const HomePage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<HomeController>(() => HomeController());
      }),
    ),
    
    GetPage(
      name: AppRoutes.dashboard,
      page: () => const DashboardPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<DashboardController>(() => DashboardController());
      }),
    ),
    
    GetPage(
      name: AppRoutes.profile,
      page: () => const ProfilePage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<ProfileController>(() => ProfileController());
      }),
    ),
    
    GetPage(
      name: AppRoutes.settings,
      page: () => const SettingsPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<SettingsController>(() => SettingsController());
      }),
    ),

    // Games
    GetPage(
      name: AppRoutes.games,
      page: () => const GamesPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<GamesController>(() => GamesController());
      }),
    ),
    
    GetPage(
      name: AppRoutes.gameDetails,
      page: () => const GameDetailsPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<GameDetailsController>(() => GameDetailsController());
      }),
    ),

    // Features
    GetPage(
      name: AppRoutes.leaderboard,
      page: () => const LeaderboardPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<LeaderboardController>(() => LeaderboardController());
      }),
    ),
    
    GetPage(
      name: AppRoutes.achievements,
      page: () => const AchievementsPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<AchievementsController>(() => AchievementsController());
      }),
    ),
    
    GetPage(
      name: AppRoutes.notifications,
      page: () => const NotificationsPage(),
      binding: BindingsBuilder(() {
        Get.lazyPut<NotificationsController>(() => NotificationsController());
      }),
    ),

    // Utility Pages
    GetPage(
      name: AppRoutes.help,
      page: () => const HelpPage(),
    ),
    
    GetPage(
      name: AppRoutes.about,
      page: () => const AboutPage(),
    ),
    
    GetPage(
      name: AppRoutes.privacyPolicy,
      page: () => const PrivacyPolicyPage(),
    ),
    
    GetPage(
      name: AppRoutes.termsOfService,
      page: () => const TermsOfServicePage(),
    ),

    // Error Pages
    GetPage(
      name: AppRoutes.notFound,
      page: () => const NotFoundPage(),
    ),
    
    GetPage(
      name: AppRoutes.error,
      page: () => const ErrorPage(),
    ),
  ];
}

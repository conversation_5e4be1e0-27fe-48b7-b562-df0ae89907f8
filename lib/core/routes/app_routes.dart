abstract class AppRoutes {
  // Authentication Routes
  static const String splash = '/splash';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  
  // Main App Routes
  static const String home = '/home';
  static const String dashboard = '/dashboard';
  static const String profile = '/profile';
  static const String settings = '/settings';
  
  // Feature Routes
  static const String games = '/games';
  static const String gameDetails = '/games/:id';
  static const String leaderboard = '/leaderboard';
  static const String achievements = '/achievements';
  
  // Utility Routes
  static const String notifications = '/notifications';
  static const String help = '/help';
  static const String about = '/about';
  static const String privacyPolicy = '/privacy-policy';
  static const String termsOfService = '/terms-of-service';
  
  // Error Routes
  static const String notFound = '/404';
  static const String error = '/error';
  
  // Helper methods
  static String gameDetailsWithId(String id) => '/games/$id';
  
  // Route validation
  static bool isAuthRoute(String route) {
    return [splash, onboarding, login, register, forgotPassword].contains(route);
  }
  
  static bool isProtectedRoute(String route) {
    return ![splash, onboarding, login, register, forgotPassword, notFound, error]
        .contains(route);
  }
}

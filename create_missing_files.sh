#!/bin/bash

# Create remaining pages
pages=(
  "profile_page.dart:Profile:person"
  "settings_page.dart:Settings:settings"
  "games/games_page.dart:Games:games"
  "games/game_details_page.dart:Game Details:gamepad"
  "leaderboard_page.dart:Leaderboard:leaderboard"
  "achievements_page.dart:Achievements:emoji_events"
  "notifications_page.dart:Notifications:notifications"
  "help_page.dart:Help:help"
  "about_page.dart:About:info"
  "privacy_policy_page.dart:Privacy Policy:privacy_tip"
  "terms_of_service_page.dart:Terms of Service:description"
  "not_found_page.dart:Page Not Found:error"
  "error_page.dart:Error:error_outline"
)

# Create remaining controllers
controllers=(
  "profile_controller.dart"
  "settings_controller.dart"
  "games/games_controller.dart"
  "games/game_details_controller.dart"
  "leaderboard_controller.dart"
  "achievements_controller.dart"
  "notifications_controller.dart"
)

# Create pages
for page_info in "${pages[@]}"; do
  IFS=':' read -r filename title icon <<< "$page_info"
  
  # Create directory if it doesn't exist
  mkdir -p "lib/views/pages/$(dirname "$filename")"
  
  # Extract class name from filename
  class_name=$(echo "$filename" | sed 's/_page\.dart$//' | sed 's/_/ /g' | sed 's/\b\w/\U&/g' | sed 's/ //g')Page
  controller_name=$(echo "$filename" | sed 's/_page\.dart$//' | sed 's/_/ /g' | sed 's/\b\w/\U&/g' | sed 's/ //g')Controller
  controller_path=$(echo "$filename" | sed 's/\.dart$//' | sed 's/_page$//')
  
  cat > "lib/views/pages/$filename" << PAGEOF
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/${controller_path}_controller.dart';
import '../../core/constants/app_colors.dart';

class $class_name extends GetView<$controller_name> {
  const $class_name({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('$title'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.$icon,
              size: 80,
              color: AppColors.primary,
            ),
            SizedBox(height: 24),
            Text(
              '$title',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            Text(
              '$title content will be implemented here',
              style: TextStyle(
                fontSize: 16,
                color: AppColors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
PAGEOF
done

# Create controllers
for controller in "${controllers[@]}"; do
  # Create directory if it doesn't exist
  mkdir -p "lib/controllers/$(dirname "$controller")"
  
  # Extract class name from filename
  class_name=$(echo "$controller" | sed 's/_controller\.dart$//' | sed 's/_/ /g' | sed 's/\b\w/\U&/g' | sed 's/ //g')Controller
  
  cat > "lib/controllers/$controller" << CONTROLLEROF
import 'package:get/get.dart';

class $class_name extends GetxController {
  // Controller implementation will be added here
}
CONTROLLEROF
done

echo "All missing files created successfully!"
